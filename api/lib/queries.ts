import { prismaClient } from '@/lib/prisma';
import type { TimerStats } from '@/lib/timerStats';
import { getAncestor } from '@prisma/client/sql';
import type { contacts } from '@prisma/client';
import type { Decimal } from '@prisma/client/runtime/library';

// Type for nested parent relationship structure
interface NestedParentRelationship {
  parent:
    | (contacts & { parent_relationships: NestedParentRelationship[] })
    | null;
  split_percentage: Decimal | null;
}

const getEffectiveDateCondition = (
  effectiveDate?: Date | undefined | null
) => ({
  AND: [
    {
      OR: [
        ...(effectiveDate ? [{ start_date: { lte: effectiveDate } }] : []),
        { start_date: null },
      ],
    },
    {
      OR: [
        ...(effectiveDate ? [{ end_date: { gte: effectiveDate } }] : []),
        { end_date: null },
      ],
    },
  ],
});

// @ts-expect-error
const recursiveSelect = (
  depth: number,
  vars?: { effectiveDate?: Date | null }
) => {
  if (depth === 0) {
    return {
      select: {
        parent: {
          include: {
            parent_relationships: {
              where: {
                state: 'active',
                parent_id: { not: null },
                ...getEffectiveDateCondition(vars?.effectiveDate),
              },
            },
          },
        },
        split_percentage: true,
      },
    };
  }

  return {
    select: {
      parent: {
        include: {
          parent_relationships: {
            where: {
              state: 'active',
              ...getEffectiveDateCondition(vars?.effectiveDate),
            },
            ...recursiveSelect(depth - 1, vars),
          },
        },
      },
      split_percentage: true,
    },
  };
};

export const findContactWithAncestorsCTE = async (
  strId: string,
  effectiveDate: Date | undefined | null,
  timerStats?: TimerStats
) => {
  const start = timerStats?.start();

  const result = await prismaClient.$queryRawTyped(
    getAncestor(strId, effectiveDate || new Date())
  );

  // @ts-expect-error
  const duration = timerStats?.end('findContactWithAncestorsCTE', start);
  // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  console.info(
    `findContactWithAncestorsCTE took ${duration?.toFixed(2)}s (total: ${timerStats
      ?.get('findContactWithAncestorsCTE')
      ?.toFixed(2)}s)\n${timerStats?.logString('findContactWithAncestorsCTE')}`
  );

  return result;
};

export const findContactInclAncestorsCTE = async (
  strId: string,
  effectiveDate: Date | undefined | null,
  timerStats?: TimerStats
) => {
  const start = timerStats?.start();

  const result = await prismaClient.$queryRawTyped(
    getAncestor(strId, effectiveDate || new Date())
  );

  // @ts-expect-error
  const duration = timerStats?.end('findContactInclAncestorsCTE', start);
  // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  console.info(
    `findContactInclAncestorsCTE took ${duration?.toFixed(2)}s (total: ${timerStats
      ?.get('findContactInclAncestorsCTE')
      ?.toFixed(2)}s)\n${timerStats?.logString('findContactInclAncestorsCTE')}`
  );

  if (!result || result.length === 0) {
    return null;
  }

  // Transform the flat CTE result into the same structure as findContactInclAncestors
  const baseContact = result[0]; // The first record is always the requested contact (level 0)

  // Get all additional contact fields for the base contact
  const fullBaseContact = await prismaClient.contacts.findFirst({
    where: { str_id: baseContact.str_id, state: 'active' },
  });

  if (!fullBaseContact) {
    return null;
  }

  // Build parent_relationships array from the CTE result
  const parentRelationships: NestedParentRelationship[] = [];

  // Helper function to build nested parent relationships recursively
  const buildNestedParentRelationships = async (
    startIndex: number
  ): Promise<NestedParentRelationship[]> => {
    const nestedRelationships: NestedParentRelationship[] = [];

    if (startIndex < result.length) {
      const currentLevel = result[startIndex];
      const previousLevel = result[startIndex - 1];

      if (currentLevel.id !== null) {
        // Get full contact details for the parent
        const parentContact = await prismaClient.contacts.findFirst({
          where: { id: currentLevel.id, state: 'active' },
        });

        if (parentContact) {
          const nestedParentRelationships =
            await buildNestedParentRelationships(startIndex + 1);

          nestedRelationships.push({
            parent: {
              ...parentContact,
              parent_relationships:
                nestedParentRelationships.length > 0
                  ? nestedParentRelationships
                  : [{ parent: null, split_percentage: null }],
            },
            split_percentage: previousLevel.split_percentage,
          });
        }
      }
    }

    return nestedRelationships;
  };

  // Build the immediate parent relationship
  if (result.length > 1) {
    const immediateParentLevel = result[1];
    const baseLevel = result[0];

    if (immediateParentLevel.id !== null) {
      const parentContact = await prismaClient.contacts.findFirst({
        where: { id: immediateParentLevel.id, state: 'active' },
      });

      if (parentContact) {
        const nestedParentRelationships =
          await buildNestedParentRelationships(2);

        parentRelationships.push({
          parent: {
            ...parentContact,
            parent_relationships:
              nestedParentRelationships.length > 0
                ? nestedParentRelationships
                : [{ parent: null, split_percentage: null }],
          },
          split_percentage: baseLevel.split_percentage,
        });
      }
    }
  }

  // If no parent relationships found, add the null parent structure
  if (parentRelationships.length === 0) {
    parentRelationships.push({
      parent: null,
      split_percentage: null,
    });
  }

  // Return the contact with the same structure as findContactInclAncestors
  return {
    ...fullBaseContact,
    parent_relationships: parentRelationships,
  };
};

export const findContactWithAncestorsIterative = async (
  strId: string,
  effectiveDate: Date | undefined,
  maxDepth: number = 50,
  timerStats?: TimerStats
) => {
  const start = timerStats?.start();
  const ancestors = [];
  let currentContactId: number | null = null;

  const initialContact = await prismaClient.contacts.findFirst({
    where: { str_id: strId, state: 'active' },
    include: {
      parent_relationships: {
        where: {
          state: 'active',
          ...getEffectiveDateCondition(effectiveDate),
        },
        select: {
          parent_id: true,
          split_percentage: true,
        },
      },
    },
  });

  if (!initialContact) {
    return null;
  }

  ancestors.push(initialContact);
  // Prevent self reference: if parent ID equals own ID, set to null
  const firstParentId = initialContact.parent_relationships[0]?.parent_id;
  currentContactId =
    firstParentId && firstParentId !== initialContact.id ? firstParentId : null;

  let depth = 0;
  const visitedIds = new Set([initialContact.id]);

  while (currentContactId && depth < maxDepth) {
    if (visitedIds.has(currentContactId)) {
      break;
    }

    const parentContact = await prismaClient.contacts.findFirst({
      where: { id: currentContactId, state: 'active' },
      include: {
        parent_relationships: {
          where: {
            state: 'active',
            ...getEffectiveDateCondition(effectiveDate),
          },
          select: {
            parent_id: true,
            split_percentage: true,
          },
        },
      },
    });

    if (!parentContact) {
      break;
    }

    ancestors.push(parentContact);
    visitedIds.add(parentContact.id);
    // Prevent self reference: ensure next parent ID is not equal to current contact ID
    const nextParentId = parentContact.parent_relationships[0]?.parent_id;
    currentContactId =
      nextParentId && nextParentId !== parentContact.id ? nextParentId : null;
    depth++;
  }

  // @ts-expect-error
  const duration = timerStats?.end('findContactWithAncestorsIterative', start);
  // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  console.info(
    `findContactWithAncestorsIterative took ${duration?.toFixed(2)}s (total: ${timerStats
      ?.get('findContactWithAncestorsIterative')
      ?.toFixed(
        2
      )}s)\n${timerStats?.logString('findContactWithAncestorsIterative')}`
  );

  return {
    contact: ancestors[0],
    ancestors: ancestors.slice(1),
    totalLevels: ancestors.length - 1,
  };
};

export const findContactInclAncestors = async (
  strId: string,
  effectiveDate: Date | undefined | null,
  timerStats?: TimerStats
) => {
  const start = timerStats?.start();
  const query = {
    where: {
      str_id: strId,
      state: 'active',
    },
    accountInject: false,
    include: {
      parent_relationships: {
        where: {
          state: 'active',
          ...getEffectiveDateCondition(effectiveDate),
        },
        ...recursiveSelect(9, { effectiveDate }),
      },
    },
  };

  const result = await prismaClient.contacts.findFirst(query);
  // @ts-expect-error
  const duration = timerStats?.end('findContactInclAncestors', start);
  // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  console.info(
    `findContactInclAncestors took ${duration?.toFixed(2)}s (total: ${timerStats
      ?.get('findContactInclAncestors')
      ?.toFixed(2)}s)\n${timerStats?.logString('findContactInclAncestors')}`
  );
  return result;
};
