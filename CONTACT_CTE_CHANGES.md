# Contact CTE Function Changes

## Summary

Modified the `findContactWithAncestorsCTE` function to return a structure consistent with `findContactInclAncestors`, while maintaining backward compatibility.

## Changes Made

### 1. Added New Function: `findContactInclAncestorsCTE`

Created a new function `findContactInclAncestorsCTE` in `api/lib/queries.ts` that:

- Uses the same efficient CTE query as `findContactWithAncestorsCTE`
- Returns the same nested structure as `findContactInclAncestors`
- Transforms the flat CTE result into the expected hierarchical structure
- Maintains all contact fields from the database
- Properly handles the `parent_relationships` array with nested parent structures

### 2. Preserved Original Function

The original `findContactWithAncestorsCTE` function remains unchanged to maintain backward compatibility with existing code that depends on the flat array structure.

### 3. Type Definitions

Added proper TypeScript type definitions:

```typescript
interface NestedParentRelationship {
  parent:
    | (contacts & { parent_relationships: NestedParentRelationship[] })
    | null;
  split_percentage: Decimal | null;
}
```

## Structure Comparison

### Original `findContactInclAncestors` Result:
```javascript
{
  id: 3471,
  str_id: "ZvcThw_fUWLcvN18vL3Oj",
  name: "Shao, Tina X",
  // ... all contact fields
  parent_relationships: [
    {
      parent: {
        id: 2833,
        str_id: "45uIYHKLb5lI9OJVyx_dD",
        name: "Xu, Chuntao",
        // ... all parent contact fields
        parent_relationships: [
          {
            parent: {
              id: 1938,
              // ... grandparent fields
              parent_relationships: [
                { parent: null, split_percentage: null }
              ]
            },
            split_percentage: null
          }
        ]
      },
      split_percentage: null
    }
  ]
}
```

### New `findContactInclAncestorsCTE` Result:
Returns the exact same structure as above, but uses the efficient CTE query internally.

### Original `findContactWithAncestorsCTE` Result (unchanged):
```javascript
[
  {
    id: 3471,
    str_id: "ZvcThw_fUWLcvN18vL3Oj",
    name: "Shao, Tina X",
    level: 0,
    path: [3471]
  },
  {
    id: 2833,
    str_id: "45uIYHKLb5lI9OJVyx_dD", 
    name: "Xu, Chuntao",
    level: 1,
    path: [3471, 2833]
  },
  // ... more ancestors
]
```

## Usage

To use the new function that returns the same structure as `findContactInclAncestors`:

```typescript
import { findContactInclAncestorsCTE } from '@/lib/queries';

const contact = await findContactInclAncestorsCTE(
  strId, 
  effectiveDate, 
  timerStats
);
```

## Benefits

1. **Performance**: Uses efficient CTE query instead of recursive Prisma queries
2. **Compatibility**: Returns the same structure as `findContactInclAncestors`
3. **Backward Compatibility**: Original `findContactWithAncestorsCTE` unchanged
4. **Type Safety**: Proper TypeScript types for the nested structure

## Files Modified

- `api/lib/queries.ts`: Added new function and type definitions

## Testing

The function should be tested with existing contact data to ensure:
1. Structure matches `findContactInclAncestors` exactly
2. All contact fields are properly included
3. Nested parent relationships are correctly built
4. Performance is improved compared to the original recursive approach
